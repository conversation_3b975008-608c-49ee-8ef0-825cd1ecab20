// Cloudinary configuration and upload utilities
const CLOUDINARY_CLOUD_NAME = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME || 'dk4i5sxqq';
const CLOUDINARY_API_KEY = import.meta.env.VITE_CLOUDINARY_API_KEY || '724224496477429';
// NrufH498l_I55ABLVm0MW5IcnxE
// Cloudinary configuration

export interface CloudinaryUploadResult {
  public_id: string;
  secure_url: string;
  original_filename: string;
  format: string;
  bytes: number;
  created_at: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

/**
 * Upload file to Cloudinary
 * @param file - File to upload
 * @param onProgress - Progress callback function
 * @param folder - Folder to upload to (default: 'assignments')
 * @returns Promise with upload result
 */
export const uploadToCloudinary = async (
  file: File,
  onProgress?: (progress: UploadProgress) => void,
  folder: string = 'assignments'
): Promise<CloudinaryUploadResult> => {
  if (!CLOUDINARY_CLOUD_NAME) {
    throw new Error('Cloudinary cloud name not configured');
  }

  // Try with the most basic unsigned upload first
  try {
    console.log('Attempting basic unsigned upload to Cloudinary');

    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', 'ml_default'); // Cloudinary's default preset

    const uploadUrl = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/upload`;

    const result = await attemptUpload(uploadUrl, formData, onProgress);
    console.log('Basic unsigned upload successful');
    return result;

  } catch (error) {
    console.warn('Basic unsigned upload failed:', error);

    // Try with a custom preset if ml_default doesn't work
    try {
      console.log('Attempting upload with assignment_uploads preset');

      const formData = new FormData();
      formData.append('file', file);
      formData.append('upload_preset', 'assignment_uploads');
      formData.append('folder', folder);

      const uploadUrl = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/upload`;

      const result = await attemptUpload(uploadUrl, formData, onProgress);
      console.log('Custom preset upload successful');
      return result;

    } catch (customError) {
      console.warn('Custom preset upload failed:', customError);
    }
  }

  // If unsigned upload fails, try signed upload with API key
  if (CLOUDINARY_API_KEY) {
    try {
      console.log('Attempting signed upload to Cloudinary');

      const timestamp = Math.round(Date.now() / 1000);
      const formData = new FormData();
      formData.append('file', file);
      formData.append('timestamp', timestamp.toString());
      formData.append('api_key', CLOUDINARY_API_KEY);

      // Add folder for organization
      formData.append('folder', folder);

      // Add resource type based on file type
      if (file.type.startsWith('image/')) {
        formData.append('resource_type', 'image');
      } else if (file.type === 'application/pdf') {
        formData.append('resource_type', 'raw');
      } else {
        formData.append('resource_type', 'auto');
      }

      const uploadUrl = `https://api.cloudinary.com/v1_1/${CLOUDINARY_CLOUD_NAME}/upload`;

      const result = await attemptUpload(uploadUrl, formData, onProgress);
      console.log('Signed upload successful');
      return result;

    } catch (error) {
      console.error('Signed upload also failed:', error);
      throw error;
    }
  }

  throw new Error('All upload methods failed. Please check your Cloudinary configuration.');
};

// Helper function to attempt upload with a specific configuration
const attemptUpload = (uploadUrl: string, formData: FormData, onProgress?: (progress: UploadProgress) => void): Promise<CloudinaryUploadResult> => {

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    // Track upload progress
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress: UploadProgress = {
            loaded: event.loaded,
            total: event.total,
            percentage: Math.round((event.loaded / event.total) * 100)
          };
          onProgress(progress);
        }
      });
    }

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const result = JSON.parse(xhr.responseText);
          resolve(result);
        } catch (error) {
          reject(new Error('Failed to parse upload response'));
        }
      } else {
        // Log detailed error information
        console.error('Cloudinary upload failed:', {
          status: xhr.status,
          statusText: xhr.statusText,
          response: xhr.responseText,
          url: uploadUrl
        });

        let errorMessage = `Upload failed with status: ${xhr.status}`;
        try {
          const errorResponse = JSON.parse(xhr.responseText);
          if (errorResponse.error && errorResponse.error.message) {
            errorMessage += ` - ${errorResponse.error.message}`;
          }
        } catch (e) {
          // If response is not JSON, include raw response
          if (xhr.responseText) {
            errorMessage += ` - ${xhr.responseText}`;
          }
        }

        reject(new Error(errorMessage));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed due to network error'));
    });

    xhr.open('POST', uploadUrl);
    xhr.send(formData);
  });
};

/**
 * Delete file from Cloudinary
 * @param publicId - Public ID of the file to delete
 * @returns Promise with deletion result
 */
export const deleteFromCloudinary = async (publicId: string): Promise<void> => {
  // Note: Deletion requires server-side implementation with API secret
  // This is a placeholder for the client-side interface
  console.warn('File deletion should be implemented on the server side for security');
  
  // In a real implementation, you would call your backend API
  // which would handle the deletion using the Cloudinary Admin API
  throw new Error('File deletion not implemented - requires server-side implementation');
};

/**
 * Get optimized URL for file display
 * @param publicId - Public ID of the file
 * @param options - Transformation options
 * @returns Optimized URL
 */
export const getOptimizedUrl = (
  publicId: string,
  options: {
    width?: number;
    height?: number;
    quality?: 'auto' | number;
    format?: 'auto' | string;
  } = {}
): string => {
  if (!CLOUDINARY_CLOUD_NAME) {
    throw new Error('Cloudinary cloud name not configured');
  }

  const { width, height, quality = 'auto', format = 'auto' } = options;
  
  let transformations = [];
  
  if (width) transformations.push(`w_${width}`);
  if (height) transformations.push(`h_${height}`);
  if (quality) transformations.push(`q_${quality}`);
  if (format) transformations.push(`f_${format}`);
  
  const transformString = transformations.length > 0 ? `${transformations.join(',')}/` : '';
  
  return `https://res.cloudinary.com/${CLOUDINARY_CLOUD_NAME}/image/upload/${transformString}${publicId}`;
};

/**
 * Validate file for upload
 * @param file - File to validate
 * @returns Validation result
 */
export const validateFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = [
    'application/pdf',
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/avif',
    'image/gif'
  ];

  const allowedExtensions = ['pdf', 'png', 'jpg', 'jpeg', 'avif', 'gif'];

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'File size exceeds 10MB limit'
    };
  }

  // Check MIME type first
  let isValidType = allowedTypes.includes(file.type);

  // If MIME type check fails, check file extension as fallback
  if (!isValidType) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    isValidType = extension ? allowedExtensions.includes(extension) : false;
  }

  if (!isValidType) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    return {
      valid: false,
      error: `File type not supported. File: ${file.name} (${file.type || 'unknown type'}${extension ? `, .${extension}` : ''}). Allowed types: PDF, PNG, JPG, JPEG, AVIF, GIF`
    };
  }

  return { valid: true };
};
